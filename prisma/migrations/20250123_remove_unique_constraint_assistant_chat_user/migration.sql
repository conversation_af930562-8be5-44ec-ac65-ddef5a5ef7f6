-- Remove unique constraint on assistant_chat_users to allow multiple sessions per user/chat
-- This enables creating new assistant_chat_user records for each AI interaction

-- Drop the existing unique constraint
ALTER TABLE "assistant_chat_users" DROP CONSTRAINT IF EXISTS "assistant_chat_users_chat_id_user_id_key";

-- Add a session_id field to help identify different AI conversation sessions
ALTER TABLE "assistant_chat_users" ADD COLUMN IF NOT EXISTS "session_id" TEXT;

-- Create index for better performance on queries
CREATE INDEX IF NOT EXISTS "assistant_chat_users_user_id_chat_id_idx" ON "assistant_chat_users"("user_id", "chat_id");
CREATE INDEX IF NOT EXISTS "assistant_chat_users_session_id_idx" ON "assistant_chat_users"("session_id");
CREATE INDEX IF NOT EXISTS "assistant_chat_users_created_at_idx" ON "assistant_chat_users"("created_at");
